// import 'dart:convert';

// import 'package:connectone/bai_models/offers_res.dart';

// SellerOffers sellerFromJson(String str) =>
//     SellerOffers.fromJson(json.decode(str));

// String sellerToJson(SellerOffers data) => json.encode(data.toJson());

// class SellerOffers {
//   List<SellerOffer>? variants;
//   List<PreviousQuote>? previousQuotes;
//   List<AlreadySubmittedQuote>? alreadySubmittedQuote;

//   SellerOffers({
//     this.variants,
//     this.previousQuotes,
//     this.alreadySubmittedQuote,
//   });

//   factory SellerOffers.fromJson(Map<String, dynamic> json) => SellerOffers(
//         variants: json["variants"] == null
//             ? []
//             : List<SellerOffer>.from(
//                 json["variants"]!.map((x) => SellerOffer.fromJson(x))),
//         previousQuotes: json["previousQuotes"] == null
//             ? []
//             : List<PreviousQuote>.from(
//                 json["previousQuotes"]!.map((x) => PreviousQuote.fromJson(x))),
//         alreadySubmittedQuote: json["alreadySubmittedQuote"] == null
//             ? []
//             : List<AlreadySubmittedQuote>.from(json["alreadySubmittedQuote"]!
//                 .map((x) => AlreadySubmittedQuote.fromJson(x))),
//       );

//   Map<String, dynamic> toJson() => {
//         "variants": variants == null
//             ? []
//             : List<dynamic>.from(variants!.map((x) => x.toJson())),
//         "previousQuotes": previousQuotes == null
//             ? []
//             : List<dynamic>.from(previousQuotes!.map((x) => x.toJson())),
//         "alreadySubmittedQuote": alreadySubmittedQuote == null
//             ? []
//             : List<dynamic>.from(alreadySubmittedQuote!.map((x) => x.toJson())),
//       };
// }

// class AlreadySubmittedQuote {
//   int? id;
//   String? remarks;
//   num? quantity;
//   DateTime? createdAt;
//   dynamic baiMember;
//   dynamic typeOfGstFiling;
//   dynamic rating;
//   dynamic statusName;
//   dynamic negotiatedPrice;
//   String? negotiationStatus;
//   String? negotiationStatusName;
//   dynamic negotiatedCustomerId;
//   dynamic negotiationBuyrComment;
//   dynamic negotiationMmbrComment;
//   List<Media>? medias;
//   int? mvtItemId;
//   String? mvtItemName;
//   int? optionGroupId;
//   String? optionGroupName;
//   String? optionName;
//   int? optionId;
//   num? offerPrice;
//   int? customerId;
//   int? vendorId;
//   int? prchOrdrId;
//   String? statusCd;
//   dynamic vendorName;
//   dynamic vendorCity;
//   int? variant1OptionId;
//   String? variant1OptionName;
//   int? variant2OptionId;
//   String? variant2OptionName;
//   dynamic variant3OptionId;
//   dynamic variant3OptionName;
//   int? variant1OptionGroupId;
//   String? variant1OptionGroupName;
//   int? variant2OptionGroupId;
//   String? variant2OptionGroupName;
//   dynamic variant3OptionGroupId;
//   dynamic variant3OptionGroupName;
//   dynamic vendorPhone;
//   dynamic customerName;
//   dynamic customerPhone;
//   List<NegotiationHistory>? negotiationHistory;

//   AlreadySubmittedQuote({
//     this.id,
//     this.remarks,
//     this.quantity,
//     this.createdAt,
//     this.baiMember,
//     this.typeOfGstFiling,
//     this.rating,
//     this.statusName,
//     this.negotiatedPrice,
//     this.negotiationStatus,
//     this.negotiationStatusName,
//     this.negotiatedCustomerId,
//     this.negotiationBuyrComment,
//     this.negotiationMmbrComment,
//     this.medias,
//     this.mvtItemId,
//     this.mvtItemName,
//     this.optionGroupId,
//     this.optionGroupName,
//     this.optionName,
//     this.optionId,
//     this.offerPrice,
//     this.customerId,
//     this.vendorId,
//     this.prchOrdrId,
//     this.statusCd,
//     this.vendorName,
//     this.vendorCity,
//     this.variant1OptionId,
//     this.variant1OptionName,
//     this.variant2OptionId,
//     this.variant2OptionName,
//     this.variant3OptionId,
//     this.variant3OptionName,
//     this.variant1OptionGroupId,
//     this.variant1OptionGroupName,
//     this.variant2OptionGroupId,
//     this.variant2OptionGroupName,
//     this.variant3OptionGroupId,
//     this.variant3OptionGroupName,
//     this.vendorPhone,
//     this.customerName,
//     this.customerPhone,
//     this.negotiationHistory,
//   });

//   factory AlreadySubmittedQuote.fromJson(Map<String, dynamic> json) =>
//       AlreadySubmittedQuote(
//         id: json["id"],
//         remarks: json["remarks"],
//         quantity: json["quantity"],
//         createdAt: json["created_at"] == null
//             ? null
//             : DateTime.parse(json["created_at"]),
//         baiMember: json["bai_member"],
//         typeOfGstFiling: json["type_of_gst_filing"],
//         rating: json["rating"],
//         statusName: json["status_name"],
//         negotiatedPrice: json["negotiated_price"],
//         negotiationStatus: json["negotiation_status"],
//         negotiationStatusName: json["negotiation_status_name"],
//         negotiatedCustomerId: json["negotiated_customer_id"],
//         negotiationBuyrComment: json["negotiation_buyr_comment"],
//         negotiationMmbrComment: json["negotiation_mmbr_comment"],
//         medias: json["medias"] == null
//             ? []
//             : List<Media>.from(json["medias"]!.map((x) => Media.fromJson(x))),
//         mvtItemId: json["mvt_item_id"],
//         mvtItemName: json["mvt_item_name"],
//         optionGroupId: json["option_group_id"],
//         optionGroupName: json["option_group_name"],
//         optionName: json["option_name"],
//         optionId: json["option_id"],
//         offerPrice: json["offer_price"],
//         customerId: json["customer_id"],
//         vendorId: json["vendor_id"],
//         prchOrdrId: json["prch_ordr_id"],
//         statusCd: json["status_cd"],
//         vendorName: json["vendor_name"],
//         vendorCity: json["vendor_city"],
//         variant1OptionId: json["variant_1_option_id"],
//         variant1OptionName: json["variant_1_option_name"],
//         variant2OptionId: json["variant_2_option_id"],
//         variant2OptionName: json["variant_2_option_name"],
//         variant3OptionId: json["variant_3_option_id"],
//         variant3OptionName: json["variant_3_option_name"],
//         variant1OptionGroupId: json["variant_1_option_group_id"],
//         variant1OptionGroupName: json["variant_1_option_group_name"],
//         variant2OptionGroupId: json["variant_2_option_group_id"],
//         variant2OptionGroupName: json["variant_2_option_group_name"],
//         variant3OptionGroupId: json["variant_3_option_group_id"],
//         variant3OptionGroupName: json["variant_3_option_group_name"],
//         vendorPhone: json["vendor_phone"],
//         customerName: json["customer_name"],
//         customerPhone: json["customer_phone"],
//         negotiationHistory: json["negotiation_history"] == null
//             ? []
//             : List<NegotiationHistory>.from(json["negotiation_history"]!
//                 .map((x) => NegotiationHistory.fromJson(x))),
//       );

//   Map<String, dynamic> toJson() => {
//         "id": id,
//         "remarks": remarks,
//         "quantity": quantity,
//         "created_at": createdAt?.toIso8601String(),
//         "bai_member": baiMember,
//         "type_of_gst_filing": typeOfGstFiling,
//         "rating": rating,
//         "status_name": statusName,
//         "negotiated_price": negotiatedPrice,
//         "negotiation_status": negotiationStatus,
//         "negotiation_status_name": negotiationStatusName,
//         "negotiated_customer_id": negotiatedCustomerId,
//         "negotiation_buyr_comment": negotiationBuyrComment,
//         "negotiation_mmbr_comment": negotiationMmbrComment,
//         "medias": medias == null
//             ? []
//             : List<dynamic>.from(medias!.map((x) => x.toJson())),
//         "mvt_item_id": mvtItemId,
//         "mvt_item_name": mvtItemName,
//         "option_group_id": optionGroupId,
//         "option_group_name": optionGroupName,
//         "option_name": optionName,
//         "option_id": optionId,
//         "offer_price": offerPrice,
//         "customer_id": customerId,
//         "vendor_id": vendorId,
//         "prch_ordr_id": prchOrdrId,
//         "status_cd": statusCd,
//         "vendor_name": vendorName,
//         "vendor_city": vendorCity,
//         "variant_1_option_id": variant1OptionId,
//         "variant_1_option_name": variant1OptionName,
//         "variant_2_option_id": variant2OptionId,
//         "variant_2_option_name": variant2OptionName,
//         "variant_3_option_id": variant3OptionId,
//         "variant_3_option_name": variant3OptionName,
//         "variant_1_option_group_id": variant1OptionGroupId,
//         "variant_1_option_group_name": variant1OptionGroupName,
//         "variant_2_option_group_id": variant2OptionGroupId,
//         "variant_2_option_group_name": variant2OptionGroupName,
//         "variant_3_option_group_id": variant3OptionGroupId,
//         "variant_3_option_group_name": variant3OptionGroupName,
//         "vendor_phone": vendorPhone,
//         "customer_name": customerName,
//         "customer_phone": customerPhone,
//       };
// }

// class Media {
//   dynamic mediaTitle;
//   String? previewUrl;
//   String? url;

//   Media({
//     this.mediaTitle,
//     this.previewUrl,
//     this.url,
//   });

//   factory Media.fromJson(Map<String, dynamic> json) => Media(
//         mediaTitle: json["mediaTitle"],
//         previewUrl: json["previewUrl"],
//         url: json["url"],
//       );

//   Map<String, dynamic> toJson() => {
//         "mediaTitle": mediaTitle,
//         "previewUrl": previewUrl,
//         "url": url,
//       };
// }

// class PreviousQuote {
//   num? id;
//   num? mvtItemId;
//   String? mvtItemName;
//   num? optionGroupId;
//   String? optionGroupName;
//   String? optionName;
//   num? optionId;
//   num? offerPrice;
//   num? customerId;
//   num? vendorId;
//   num? prchOrdrId;
//   String? statusCd;
//   num? variant1OptionId;
//   String? variant1OptionName;
//   num? variant2OptionId;
//   String? variant2OptionName;
//   num? variant3OptionId;
//   String? variant3OptionName;
//   num? variant1OptionGroupId;
//   String? variant1OptionGroupName;
//   num? variant2OptionGroupId;
//   String? variant2OptionGroupName;
//   num? variant3OptionGroupId;
//   String? variant3OptionGroupName;
//   DateTime? createdAt;
//   DateTime? updatedAt;
//   dynamic negotiatedPrice;
//   dynamic negotiatedCustomerId;
//   dynamic negotiationStatus;
//   dynamic negotiationBuyrComment;
//   dynamic negotiationMmbrComment;
//   String? remarks;
//   num? quantity;

//   PreviousQuote({
//     this.id,
//     this.mvtItemId,
//     this.mvtItemName,
//     this.optionGroupId,
//     this.optionGroupName,
//     this.optionName,
//     this.optionId,
//     this.offerPrice,
//     this.customerId,
//     this.vendorId,
//     this.prchOrdrId,
//     this.statusCd,
//     this.variant1OptionId,
//     this.variant1OptionName,
//     this.variant2OptionId,
//     this.variant2OptionName,
//     this.variant3OptionId,
//     this.variant3OptionName,
//     this.variant1OptionGroupId,
//     this.variant1OptionGroupName,
//     this.variant2OptionGroupId,
//     this.variant2OptionGroupName,
//     this.variant3OptionGroupId,
//     this.variant3OptionGroupName,
//     this.createdAt,
//     this.updatedAt,
//     this.negotiatedPrice,
//     this.negotiatedCustomerId,
//     this.negotiationStatus,
//     this.negotiationBuyrComment,
//     this.negotiationMmbrComment,
//     this.remarks,
//     this.quantity,
//   });

//   factory PreviousQuote.fromJson(Map<String, dynamic> json) => PreviousQuote(
//         id: json["id"],
//         mvtItemId: json["mvtItemId"],
//         mvtItemName: json["mvtItemName"],
//         optionGroupId: json["optionGroupId"],
//         optionGroupName: json["optionGroupName"],
//         optionName: json["optionName"],
//         optionId: json["optionId"],
//         offerPrice: json["offerPrice"],
//         customerId: json["customerId"],
//         vendorId: json["vendorId"],
//         prchOrdrId: json["prchOrdrId"],
//         statusCd: json["statusCd"],
//         variant1OptionId: json["variant1OptionId"],
//         variant1OptionName: json["variant1OptionName"],
//         variant2OptionId: json["variant2OptionId"],
//         variant2OptionName: json["variant2OptionName"],
//         variant3OptionId: json["variant3OptionId"],
//         variant3OptionName: json["variant3OptionName"],
//         variant1OptionGroupId: json["variant1OptionGroupId"],
//         variant1OptionGroupName: json["variant1OptionGroupName"],
//         variant2OptionGroupId: json["variant2OptionGroupId"],
//         variant2OptionGroupName: json["variant2OptionGroupName"],
//         variant3OptionGroupId: json["variant3OptionGroupId"],
//         variant3OptionGroupName: json["variant3OptionGroupName"],
//         createdAt: json["createdAt"] == null
//             ? null
//             : DateTime.parse(json["createdAt"]),
//         updatedAt: json["updatedAt"] == null
//             ? null
//             : DateTime.parse(json["updatedAt"]),
//         negotiatedPrice: json["negotiatedPrice"],
//         negotiatedCustomerId: json["negotiatedCustomerId"],
//         negotiationStatus: json["negotiationStatus"],
//         negotiationBuyrComment: json["negotiationBuyrComment"],
//         negotiationMmbrComment: json["negotiationMmbrComment"],
//         remarks: json["remarks"],
//         quantity: json["quantity"],
//       );

//   Map<String, dynamic> toJson() => {
//         "id": id,
//         "mvtItemId": mvtItemId,
//         "mvtItemName": mvtItemName,
//         "optionGroupId": optionGroupId,
//         "optionGroupName": optionGroupName,
//         "optionName": optionName,
//         "optionId": optionId,
//         "offerPrice": offerPrice,
//         "customerId": customerId,
//         "vendorId": vendorId,
//         "prchOrdrId": prchOrdrId,
//         "statusCd": statusCd,
//         "variant1OptionId": variant1OptionId,
//         "variant1OptionName": variant1OptionName,
//         "variant2OptionId": variant2OptionId,
//         "variant2OptionName": variant2OptionName,
//         "variant3OptionId": variant3OptionId,
//         "variant3OptionName": variant3OptionName,
//         "variant1OptionGroupId": variant1OptionGroupId,
//         "variant1OptionGroupName": variant1OptionGroupName,
//         "variant2OptionGroupId": variant2OptionGroupId,
//         "variant2OptionGroupName": variant2OptionGroupName,
//         "variant3OptionGroupId": variant3OptionGroupId,
//         "variant3OptionGroupName": variant3OptionGroupName,
//         "createdAt": createdAt?.toIso8601String(),
//         "updatedAt": updatedAt?.toIso8601String(),
//         "negotiatedPrice": negotiatedPrice,
//         "negotiatedCustomerId": negotiatedCustomerId,
//         "negotiationStatus": negotiationStatus,
//         "negotiationBuyrComment": negotiationBuyrComment,
//         "negotiationMmbrComment": negotiationMmbrComment,
//         "remarks": remarks,
//         "quantity": quantity,
//       };
// }

// class SellerOffer {
//   num? id;
//   num? prchOrdrId;
//   num? variant1OptionId;
//   String? variant1OptionName;
//   num? variant2OptionId;
//   String? variant2OptionName;
//   num? variant3OptionId;
//   dynamic variant3OptionName;
//   num? variant1OptionGroupId;
//   String? variant1OptionGroupName;
//   num? variant2OptionGroupId;
//   String? variant2OptionGroupName;
//   num? variant3OptionGroupId;
//   String? variant3OptionGroupName;
//   String? soldStatus;
//   num? price;
//   num? variant1Quantity;
//   dynamic variant1Unit;
//   dynamic variant2Unit;
//   dynamic variant3Unit;
//   num? variant2Quantity;
//   num? variant3Quantity;

//   SellerOffer({
//     this.id,
//     this.prchOrdrId,
//     this.variant1OptionId,
//     this.variant1OptionName,
//     this.variant2OptionId,
//     this.variant2OptionName,
//     this.variant3OptionId,
//     this.variant3OptionName,
//     this.variant1OptionGroupId,
//     this.variant1OptionGroupName,
//     this.variant2OptionGroupId,
//     this.variant2OptionGroupName,
//     this.variant3OptionGroupId,
//     this.variant3OptionGroupName,
//     this.soldStatus,
//     this.price,
//     this.variant1Quantity,
//     this.variant1Unit,
//     this.variant2Unit,
//     this.variant3Unit,
//     this.variant2Quantity,
//     this.variant3Quantity,
//   });

//   factory SellerOffer.fromJson(Map<String, dynamic> json) => SellerOffer(
//         id: json["id"],
//         prchOrdrId: json["prchOrdrId"],
//         variant1OptionId: json["variant1OptionId"],
//         variant1OptionName: json["variant1OptionName"],
//         variant2OptionId: json["variant2OptionId"],
//         variant2OptionName: json["variant2OptionName"],
//         variant3OptionId: json["variant3OptionId"],
//         variant3OptionName: json["variant3OptionName"],
//         variant1OptionGroupId: json["variant1OptionGroupId"],
//         variant1OptionGroupName: json["variant1OptionGroupName"],
//         variant2OptionGroupId: json["variant2OptionGroupId"],
//         variant2OptionGroupName: json["variant2OptionGroupName"],
//         variant3OptionGroupId: json["variant3OptionGroupId"],
//         variant3OptionGroupName: json["variant3OptionGroupName"],
//         soldStatus: json["soldStatus"],
//         price: json["price"],
//         variant1Quantity: json["variant1Quantity"],
//         variant1Unit: json["variant1Unit"],
//         variant2Unit: json["variant2Unit"],
//         variant3Unit: json["variant3Unit"],
//         variant2Quantity: json["variant2Quantity"],
//         variant3Quantity: json["variant3Quantity"],
//       );

//   Map<String, dynamic> toJson() => {
//         "id": id,
//         "prchOrdrId": prchOrdrId,
//         "variant1OptionId": variant1OptionId,
//         "variant1OptionName": variant1OptionName,
//         "variant2OptionId": variant2OptionId,
//         "variant2OptionName": variant2OptionName,
//         "variant3OptionId": variant3OptionId,
//         "variant3OptionName": variant3OptionName,
//         "variant1OptionGroupId": variant1OptionGroupId,
//         "variant1OptionGroupName": variant1OptionGroupName,
//         "variant2OptionGroupId": variant2OptionGroupId,
//         "variant2OptionGroupName": variant2OptionGroupName,
//         "variant3OptionGroupId": variant3OptionGroupId,
//         "variant3OptionGroupName": variant3OptionGroupName,
//         "soldStatus": soldStatus,
//         "price": price,
//         "variant1Quantity": variant1Quantity,
//         "variant1Unit": variant1Unit,
//         "variant2Unit": variant2Unit,
//         "variant3Unit": variant3Unit,
//         "variant2Quantity": variant2Quantity,
//         "variant3Quantity": variant3Quantity,
//       };
// }

import 'dart:convert';

import 'package:connectone/bai_models/offers_res.dart';

SellerOffers sellerFromJson(String str) =>
    SellerOffers.fromJson(json.decode(str));

// String sellerToJson(SellerOffers data) => json.encode(data.toJson());

class SellerOffers {
  List<SellerOffer>? variants;
  List<PreviousQuote>? previousQuotes;
  List<AlreadySubmittedQuote>? alreadySubmittedQuote;
  num? productGst;
  bool? isEnableGst;
  String? buyerName;
  bool? enableMrsm;
  String? mrStatus;

  SellerOffers({
    this.variants,
    this.previousQuotes,
    this.alreadySubmittedQuote,
    this.productGst,
    this.isEnableGst,
    this.buyerName,
    this.enableMrsm,
    this.mrStatus,
  });

  factory SellerOffers.fromJson(Map<String, dynamic> json) => SellerOffers(
        variants: json["variants"] == null
            ? []
            : List<SellerOffer>.from(json["variants"]!
                .where((element) => element["variants"] != null)
                .map((x) => SellerOffer.fromJson(x))),
        previousQuotes: json["previousQuotes"] == null
            ? []
            : List<PreviousQuote>.from(json["previousQuotes"]!
                .where((element) => element["previousQuotes"] != null)
                .map((x) => PreviousQuote.fromJson(x))),
        alreadySubmittedQuote: json["alreadySubmittedQuote"] != null
            ? []
            : List<AlreadySubmittedQuote>.from(json["alreadySubmittedQuote"]!
                .where((element) => element["alreadySubmittedQuote"] != null)
                .map((x) => AlreadySubmittedQuote.fromJson(x))),
        productGst: json["productGst"],
        isEnableGst: json["isEnableGst"],
        buyerName: json["buyerName"],
        enableMrsm: json["enableMRSM"],
        mrStatus: json["mrStatus"],
      );
}

class AlreadySubmittedQuote {
  int? id;
  String? remarks;
  num? quantity;
  DateTime? createdAt;
  dynamic baiMember;
  dynamic typeOfGstFiling;
  dynamic rating;
  dynamic statusName;
  dynamic negotiatedPrice;
  String? negotiationStatus;
  String? negotiationStatusName;
  dynamic negotiatedCustomerId;
  dynamic negotiationBuyrComment;
  dynamic negotiationMmbrComment;
  List<Media>? medias;
  int? mvtItemId;
  String? mvtItemName;
  int? optionGroupId;
  String? optionGroupName;
  String? optionName;
  int? optionId;
  num? offerPrice;
  int? customerId;
  int? vendorId;
  int? prchOrdrId;
  String? statusCd;
  dynamic vendorName;
  dynamic vendorCity;
  int? variant1OptionId;
  String? variant1OptionName;
  int? variant2OptionId;
  String? variant2OptionName;
  dynamic variant3OptionId;
  dynamic variant3OptionName;
  int? variant1OptionGroupId;
  String? variant1OptionGroupName;
  int? variant2OptionGroupId;
  String? variant2OptionGroupName;
  dynamic variant3OptionGroupId;
  dynamic variant3OptionGroupName;
  dynamic vendorPhone;
  dynamic customerName;
  dynamic customerPhone;
  List<NegotiationHistory>? negotiationHistory;
  num? gst;

  AlreadySubmittedQuote({
    this.id,
    this.remarks,
    this.quantity,
    this.createdAt,
    this.baiMember,
    this.typeOfGstFiling,
    this.rating,
    this.statusName,
    this.negotiatedPrice,
    this.negotiationStatus,
    this.negotiationStatusName,
    this.negotiatedCustomerId,
    this.negotiationBuyrComment,
    this.negotiationMmbrComment,
    this.medias,
    this.mvtItemId,
    this.mvtItemName,
    this.optionGroupId,
    this.optionGroupName,
    this.optionName,
    this.optionId,
    this.offerPrice,
    this.customerId,
    this.vendorId,
    this.prchOrdrId,
    this.statusCd,
    this.vendorName,
    this.vendorCity,
    this.variant1OptionId,
    this.variant1OptionName,
    this.variant2OptionId,
    this.variant2OptionName,
    this.variant3OptionId,
    this.variant3OptionName,
    this.variant1OptionGroupId,
    this.variant1OptionGroupName,
    this.variant2OptionGroupId,
    this.variant2OptionGroupName,
    this.variant3OptionGroupId,
    this.variant3OptionGroupName,
    this.vendorPhone,
    this.customerName,
    this.customerPhone,
    this.negotiationHistory,
    this.gst,
  });

  factory AlreadySubmittedQuote.fromJson(Map<String, dynamic> json) =>
      AlreadySubmittedQuote(
        id: json["id"],
        remarks: json["remarks"],
        quantity: json["quantity"] is String
            ? num.tryParse(json["quantity"]) ?? 0
            : json["quantity"] ?? 0,
        createdAt: json["created_at"] == null
            ? null
            : DateTime.parse(json["created_at"]),
        baiMember: json["bai_member"],
        typeOfGstFiling: json["type_of_gst_filing"],
        rating: json["rating"],
        statusName: json["status_name"],
        negotiatedPrice: json["negotiated_price"],
        negotiationStatus: json["negotiation_status"],
        negotiationStatusName: json["negotiation_status_name"],
        negotiatedCustomerId: json["negotiated_customer_id"],
        negotiationBuyrComment: json["negotiation_buyr_comment"],
        negotiationMmbrComment: json["negotiation_mmbr_comment"],
        medias: json["medias"] == null
            ? []
            : List<Media>.from(json["medias"]!.map((x) => Media.fromJson(x))),
        mvtItemId: json["mvt_item_id"],
        mvtItemName: json["mvt_item_name"],
        optionGroupId: json["option_group_id"],
        optionGroupName: json["option_group_name"],
        optionName: json["option_name"],
        optionId: json["option_id"],
        offerPrice: json["offer_price"] is String
            ? num.tryParse(json["offer_price"]) ?? 0
            : json["offer_price"] ?? 0,
        customerId: json["customer_id"],
        vendorId: json["vendor_id"],
        prchOrdrId: json["prch_ordr_id"],
        statusCd: json["status_cd"],
        vendorName: json["vendor_name"],
        vendorCity: json["vendor_city"],
        variant1OptionId: json["variant_1_option_id"],
        variant1OptionName: json["variant_1_option_name"],
        variant2OptionId: json["variant_2_option_id"],
        variant2OptionName: json["variant_2_option_name"],
        variant3OptionId: json["variant_3_option_id"],
        variant3OptionName: json["variant_3_option_name"],
        variant1OptionGroupId: json["variant_1_option_group_id"],
        variant1OptionGroupName: json["variant_1_option_group_name"],
        variant2OptionGroupId: json["variant_2_option_group_id"],
        variant2OptionGroupName: json["variant_2_option_group_name"],
        variant3OptionGroupId: json["variant_3_option_group_id"],
        variant3OptionGroupName: json["variant_3_option_group_name"],
        vendorPhone: json["vendor_phone"],
        customerName: json["customer_name"],
        customerPhone: json["customer_phone"],
        negotiationHistory: json["negotiation_history"] == null
            ? []
            : List<NegotiationHistory>.from(json["negotiation_history"]!
                .map((x) => NegotiationHistory.fromJson(x))),
        gst: json["gst"],
      );
}

class Media {
  dynamic mediaTitle;
  String? previewUrl;
  String? url;

  Media({
    this.mediaTitle,
    this.previewUrl,
    this.url,
  });

  factory Media.fromJson(Map<String, dynamic> json) => Media(
        mediaTitle: json["mediaTitle"],
        previewUrl: json["previewUrl"],
        url: json["url"],
      );

  Map<String, dynamic> toJson() => {
        "mediaTitle": mediaTitle,
        "previewUrl": previewUrl,
        "url": url,
      };
}

class PreviousQuote {
  num? id;
  num? mvtItemId;
  String? mvtItemName;
  num? optionGroupId;
  String? optionGroupName;
  String? optionName;
  num? optionId;
  num? offerPrice;
  num? customerId;
  num? vendorId;
  num? prchOrdrId;
  String? statusCd;
  num? variant1OptionId;
  String? variant1OptionName;
  num? variant2OptionId;
  String? variant2OptionName;
  num? variant3OptionId;
  String? variant3OptionName;
  num? variant1OptionGroupId;
  String? variant1OptionGroupName;
  num? variant2OptionGroupId;
  String? variant2OptionGroupName;
  num? variant3OptionGroupId;
  String? variant3OptionGroupName;
  DateTime? createdAt;
  DateTime? updatedAt;
  dynamic negotiatedPrice;
  dynamic negotiatedCustomerId;
  dynamic negotiationStatus;
  dynamic negotiationBuyrComment;
  dynamic negotiationMmbrComment;
  String? remarks;
  num? quantity;

  PreviousQuote({
    this.id,
    this.mvtItemId,
    this.mvtItemName,
    this.optionGroupId,
    this.optionGroupName,
    this.optionName,
    this.optionId,
    this.offerPrice,
    this.customerId,
    this.vendorId,
    this.prchOrdrId,
    this.statusCd,
    this.variant1OptionId,
    this.variant1OptionName,
    this.variant2OptionId,
    this.variant2OptionName,
    this.variant3OptionId,
    this.variant3OptionName,
    this.variant1OptionGroupId,
    this.variant1OptionGroupName,
    this.variant2OptionGroupId,
    this.variant2OptionGroupName,
    this.variant3OptionGroupId,
    this.variant3OptionGroupName,
    this.createdAt,
    this.updatedAt,
    this.negotiatedPrice,
    this.negotiatedCustomerId,
    this.negotiationStatus,
    this.negotiationBuyrComment,
    this.negotiationMmbrComment,
    this.remarks,
    this.quantity,
  });

  factory PreviousQuote.fromJson(Map<String, dynamic> json) => PreviousQuote(
        id: json["id"],
        mvtItemId: json["mvtItemId"],
        mvtItemName: json["mvtItemName"],
        optionGroupId: json["optionGroupId"],
        optionGroupName: json["optionGroupName"],
        optionName: json["optionName"],
        optionId: json["optionId"],
        offerPrice: json["offerPrice"] is String
            ? num.tryParse(json["offerPrice"]) ?? 0
            : json["offerPrice"] ?? 0,
        customerId: json["customerId"],
        vendorId: json["vendorId"],
        prchOrdrId: json["prchOrdrId"],
        statusCd: json["statusCd"],
        variant1OptionId: json["variant1OptionId"],
        variant1OptionName: json["variant1OptionName"],
        variant2OptionId: json["variant2OptionId"],
        variant2OptionName: json["variant2OptionName"],
        variant3OptionId: json["variant3OptionId"],
        variant3OptionName: json["variant3OptionName"],
        variant1OptionGroupId: json["variant1OptionGroupId"],
        variant1OptionGroupName: json["variant1OptionGroupName"],
        variant2OptionGroupId: json["variant2OptionGroupId"],
        variant2OptionGroupName: json["variant2OptionGroupName"],
        variant3OptionGroupId: json["variant3OptionGroupId"],
        variant3OptionGroupName: json["variant3OptionGroupName"],
        createdAt: json["createdAt"] == null
            ? null
            : DateTime.parse(json["createdAt"]),
        updatedAt: json["updatedAt"] == null
            ? null
            : DateTime.parse(json["updatedAt"]),
        negotiatedPrice: json["negotiatedPrice"],
        negotiatedCustomerId: json["negotiatedCustomerId"],
        negotiationStatus: json["negotiationStatus"],
        negotiationBuyrComment: json["negotiationBuyrComment"],
        negotiationMmbrComment: json["negotiationMmbrComment"],
        remarks: json["remarks"],
        quantity: json["quantity"] is String
            ? num.tryParse(json["quantity"]) ?? 0
            : json["quantity"] ?? 0,
      );
}

class SellerOffer {
  num? id;
  num? prchOrdrId;
  num? variant1OptionId;
  String? variant1OptionName;
  num? variant2OptionId;
  String? variant2OptionName;
  num? variant3OptionId;
  dynamic variant3OptionName;
  num? variant1OptionGroupId;
  String? variant1OptionGroupName;
  num? variant2OptionGroupId;
  String? variant2OptionGroupName;
  num? variant3OptionGroupId;
  String? variant3OptionGroupName;
  String? soldStatus;
  num? price;
  num? variant1Quantity;
  dynamic variant1Unit;
  dynamic variant2Unit;
  dynamic variant3Unit;
  num? variant2Quantity;
  num? variant3Quantity;

  SellerOffer({
    this.id,
    this.prchOrdrId,
    this.variant1OptionId,
    this.variant1OptionName,
    this.variant2OptionId,
    this.variant2OptionName,
    this.variant3OptionId,
    this.variant3OptionName,
    this.variant1OptionGroupId,
    this.variant1OptionGroupName,
    this.variant2OptionGroupId,
    this.variant2OptionGroupName,
    this.variant3OptionGroupId,
    this.variant3OptionGroupName,
    this.soldStatus,
    this.price,
    this.variant1Quantity,
    this.variant1Unit,
    this.variant2Unit,
    this.variant3Unit,
    this.variant2Quantity,
    this.variant3Quantity,
  });

  factory SellerOffer.fromJson(Map<String, dynamic> json) => SellerOffer(
        id: json["id"],
        prchOrdrId: json["prchOrdrId"],
        variant1OptionId: json["variant1OptionId"],
        variant1OptionName: json["variant1OptionName"],
        variant2OptionId: json["variant2OptionId"],
        variant2OptionName: json["variant2OptionName"],
        variant3OptionId: json["variant3OptionId"],
        variant3OptionName: json["variant3OptionName"],
        variant1OptionGroupId: json["variant1OptionGroupId"],
        variant1OptionGroupName: json["variant1OptionGroupName"],
        variant2OptionGroupId: json["variant2OptionGroupId"],
        variant2OptionGroupName: json["variant2OptionGroupName"],
        variant3OptionGroupId: json["variant3OptionGroupId"],
        variant3OptionGroupName: json["variant3OptionGroupName"],
        soldStatus: json["soldStatus"],
        price: json["price"] is String
            ? num.tryParse(json["price"]) ?? 0
            : json["price"] ?? 0,
        variant1Quantity: json["variant1Quantity"] is String
            ? num.tryParse(json["variant1Quantity"]) ?? 0
            : json["variant1Quantity"] ?? 0,
        variant1Unit: json["variant1Unit"],
        variant2Unit: json["variant2Unit"],
        variant3Unit: json["variant3Unit"],
        variant2Quantity: json["variant2Quantity"] is String
            ? num.tryParse(json["variant2Quantity"]) ?? 0
            : json["variant2Quantity"] ?? 0,
        variant3Quantity: json["variant3Quantity"] is String
            ? num.tryParse(json["variant3Quantity"]) ?? 0
            : json["variant3Quantity"] ?? 0,
      );
}
